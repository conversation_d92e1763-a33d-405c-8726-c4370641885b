import { useRequest } from 'ahooks';
import { useState, useCallback, useEffect } from 'react';

import { getPoiListByPage } from '../api/poiApi';

/**
 * 自定义 Hook：管理 POI 数据获取和分页
 * @param searchValue 搜索关键词
 * @returns 数据和分页控制器
 */
export const usePoiData = (searchValue: string) => {
    const [currentPage, setCurrentPage] = useState(1);
    const [pageSize] = useState(10);
    const [poiList, setPoiList] = useState<any[]>([]);
    const [total, setTotal] = useState(0);

    const { loading, run } = useRequest(
        async (page: number, size: number, search: string) => {
            const resData = await getPoiListByPage(page, size, search);

            if (!resData) {
                return null;
            }

            return resData;
        },
        {
            manual: true,
            onSuccess: (resData, [page]) => {
                if (!resData) {
                    return;
                }

                if (page === 1) {
                    // 第一页，重置列表
                    setPoiList(resData.list);
                } else {
                    // 后续页，追加到列表
                    setPoiList((prev) => [...prev, ...resData.list]);
                }
                setTotal(resData.total);
            },
        },
    );

    // 当searchValue变化时，重置并重新请求第一页
    useEffect(() => {
        setCurrentPage(1);
        setPoiList([]);
        run(1, pageSize, searchValue);
    }, [searchValue, run, pageSize]);

    const onChange = useCallback(
        (page: number, size: number) => {
            setCurrentPage(page);
            run(page, size, searchValue);
        },
        [run, searchValue],
    );

    const pagination = {
        current: currentPage,
        pageSize,
        total,
        onChange,
    };

    const data = {
        list: poiList,
        total,
    };

    return {
        data,
        pagination,
        poiList,
        loading,
    };
};
