import { usePagination } from 'ahooks';
import { useRef } from 'react';

import { getPoiListByPage } from '../api/poiApi';

/**
 * 自定义 Hook：管理 POI 数据获取和分页
 * @param searchValue 搜索关键词
 * @returns 数据和分页控制器
 */
export const usePoiData = (searchValue: string) => {
    const accumulatedListRef = useRef<any[]>([]);

    const { data, pagination, loading } = usePagination(
        async ({ current, pageSize = 10 }) => {
            const resData = await getPoiListByPage(
                current,
                pageSize,
                searchValue,
            );

            if (!resData) {
                return { list: accumulatedListRef.current, total: 0 };
            }

            // 如果是第一页，重置累积列表
            if (current === 1) {
                accumulatedListRef.current = [...resData.list];
            } else {
                // 如果是后续页，追加到累积列表
                accumulatedListRef.current = [
                    ...accumulatedListRef.current,
                    ...resData.list,
                ];
            }

            return {
                list: accumulatedListRef.current,
                total: resData.total,
            };
        },
        {
            refreshDeps: [searchValue], // 当searchValue变化时重新请求
        },
    );

    return {
        data,
        pagination,
        poiList: data?.list,
        loading,
    };
};
