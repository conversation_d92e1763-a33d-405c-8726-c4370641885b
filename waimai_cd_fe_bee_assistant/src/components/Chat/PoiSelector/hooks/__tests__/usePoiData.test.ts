import { renderHook, act } from '@testing-library/react-hooks';
import { usePoiData } from '../usePoiData';

// Mock the API
jest.mock('../api/poiApi', () => ({
    getPoiListByPage: jest.fn(),
}));

import { getPoiListByPage } from '../api/poiApi';

const mockGetPoiListByPage = getPoiListByPage as jest.MockedFunction<typeof getPoiListByPage>;

describe('usePoiData', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('should not cause infinite loop when searchValue changes', async () => {
        // Mock API response
        mockGetPoiListByPage.mockResolvedValue({
            list: [{ id: 1, name: 'Test POI' }],
            total: 1,
        });

        const { result, rerender } = renderHook(
            ({ searchValue }) => usePoiData(searchValue),
            {
                initialProps: { searchValue: '' },
            }
        );

        // Wait for initial load
        await act(async () => {
            await new Promise(resolve => setTimeout(resolve, 100));
        });

        // Change searchValue multiple times rapidly
        await act(async () => {
            rerender({ searchValue: 'test1' });
            rerender({ searchValue: 'test2' });
            rerender({ searchValue: 'test3' });
        });

        // Should not cause infinite loop or excessive API calls
        expect(mockGetPoiListByPage).toHaveBeenCalledTimes(4); // Initial + 3 changes
        expect(result.current.poiList).toBeDefined();
    });

    it('should accumulate list data correctly for pagination', async () => {
        // Mock API responses for pagination
        mockGetPoiListByPage
            .mockResolvedValueOnce({
                list: [{ id: 1, name: 'POI 1' }],
                total: 3,
            })
            .mockResolvedValueOnce({
                list: [{ id: 2, name: 'POI 2' }],
                total: 3,
            });

        const { result } = renderHook(() => usePoiData('test'));

        // Wait for initial load
        await act(async () => {
            await new Promise(resolve => setTimeout(resolve, 100));
        });

        // Load next page
        await act(async () => {
            result.current.pagination.onChange(2, 10);
            await new Promise(resolve => setTimeout(resolve, 100));
        });

        // Should accumulate both pages
        expect(result.current.poiList).toHaveLength(2);
        expect(result.current.poiList).toEqual([
            { id: 1, name: 'POI 1' },
            { id: 2, name: 'POI 2' },
        ]);
    });

    it('should reset accumulated list when searchValue changes', async () => {
        // Mock API responses
        mockGetPoiListByPage
            .mockResolvedValueOnce({
                list: [{ id: 1, name: 'POI 1' }],
                total: 1,
            })
            .mockResolvedValueOnce({
                list: [{ id: 2, name: 'POI 2' }],
                total: 1,
            });

        const { result, rerender } = renderHook(
            ({ searchValue }) => usePoiData(searchValue),
            {
                initialProps: { searchValue: 'test1' },
            }
        );

        // Wait for initial load
        await act(async () => {
            await new Promise(resolve => setTimeout(resolve, 100));
        });

        expect(result.current.poiList).toEqual([{ id: 1, name: 'POI 1' }]);

        // Change searchValue
        await act(async () => {
            rerender({ searchValue: 'test2' });
            await new Promise(resolve => setTimeout(resolve, 100));
        });

        // Should reset and show new results
        expect(result.current.poiList).toEqual([{ id: 2, name: 'POI 2' }]);
    });
});
